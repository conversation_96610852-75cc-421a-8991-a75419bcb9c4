// Mobile menu toggle
const mobileMenuButton = document.getElementById("mobile-menu-button");
const mobileMenu = document.getElementById("mobile-menu");

mobileMenuButton.addEventListener("click", () => {
  mobileMenu.classList.toggle("hidden");
});

// Car slider functionality
const carSlides = document.querySelectorAll(".car-slide");
const carDots = document.querySelectorAll(".car-dot");
const carPrev = document.querySelector(".car-prev");
const carNext = document.querySelector(".car-next");
let currentSlide = 0;

function showSlide(index) {
  carSlides.forEach((slide, i) => {
    if (i === index) {
      slide.classList.remove("inactive");
      slide.classList.add("active");
    } else {
      slide.classList.remove("active");
      slide.classList.add("inactive");
    }
  });

  carDots.forEach((dot, i) => {
    if (i === index) {
      dot.classList.remove("opacity-50");
    } else {
      dot.classList.add("opacity-50");
    }
  });

  currentSlide = index;
}

carDots.forEach((dot, i) => {
  dot.addEventListener("click", () => {
    showSlide(i);
  });
});

carPrev.addEventListener("click", () => {
  currentSlide = (currentSlide - 1 + carSlides.length) % carSlides.length;
  showSlide(currentSlide);
});

carNext.addEventListener("click", () => {
  currentSlide = (currentSlide + 1) % carSlides.length;
  showSlide(currentSlide);
});

// Auto slide change
setInterval(() => {
  currentSlide = (currentSlide + 1) % carSlides.length;
  showSlide(currentSlide);
}, 5000);

// FAQ toggle functionality
const faqToggles = document.querySelectorAll(".faq-toggle");

faqToggles.forEach((toggle) => {
  toggle.addEventListener("click", () => {
    const content = toggle.nextElementSibling;
    const icon = toggle.querySelector("i");

    content.classList.toggle("hidden");
    icon.classList.toggle("rotate-180");
  });
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  anchor.addEventListener("click", function (e) {
    e.preventDefault();

    const targetId = this.getAttribute("href");
    const targetElement = document.querySelector(targetId);

    if (targetElement) {
      window.scrollTo({
        top: targetElement.offsetTop - 80,
        behavior: "smooth",
      });

      // Close mobile menu if open
      if (!mobileMenu.classList.contains("hidden")) {
        mobileMenu.classList.add("hidden");
      }
    }
  });
});

// Animation on scroll
function animateOnScroll() {
  const elements = document.querySelectorAll(
    ".animate-fade-in, .animate-slide-left, .animate-slide-right"
  );

  elements.forEach((element) => {
    const elementPosition = element.getBoundingClientRect().top;
    const screenPosition = window.innerHeight / 1.2;

    if (elementPosition < screenPosition) {
      element.style.opacity = "1";
      element.style.transform = "translateY(0) translateX(0)";
    }
  });
}

window.addEventListener("scroll", animateOnScroll);
window.addEventListener("load", animateOnScroll);
// ----- Open / close logic -----
const fab = document.getElementById("fab");
const chatBox = document.getElementById("chatBox");
const closeBtn = document.getElementById("closeBtn");

function toggleChat(show) {
  chatBox.classList.toggle("opacity-0", !show);
  chatBox.classList.toggle("scale-90", !show);
  chatBox.classList.toggle("pointer-events-none", !show);
}

fab.addEventListener("click", () => toggleChat(true));
closeBtn.addEventListener("click", () => toggleChat(false));


document.getElementById("contactForm").addEventListener("submit", function (e) {
  e.preventDefault(); // Stop form from submitting normally

  // Get form values
  const name = document.getElementById("name").value.trim();
  const email = document.getElementById("email").value.trim();
  const phone = document.getElementById("phone").value.trim();
  const message = document.getElementById("message").value.trim();

  // WhatsApp number (no + or 0 at the start)
  const whatsappNumber = "8240637341";

  // Create message template
  const template = `Hello 👋, I would like to get in touch.\n\n🧑 Name: ${name}\n📧 Email: ${email}\n📱 Phone: ${phone}\n💬 Message: ${message}`;

  // Encode and create WhatsApp link
  const whatsappLink = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(
    template
  )}`;

  // Open in new tab
  window.open(whatsappLink, "_blank");
});

