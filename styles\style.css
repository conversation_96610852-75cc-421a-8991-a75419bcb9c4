  @keyframes fadeIn {
      from {
          opacity: 0;
          transform: translateY(20px);
      }

      to {
          opacity: 1;
          transform: translateY(0);
      }
  }

  @keyframes pulse {

      0%,
      100% {
          transform: scale(1);
      }

      50% {
          transform: scale(1.05);
      }
  }

  @keyframes slideInLeft {
      from {
          transform: translateX(-100px);
          opacity: 0;
      }

      to {
          transform: translateX(0);
          opacity: 1;
      }
  }

  @keyframes slideInRight {
      from {
          transform: translateX(100px);
          opacity: 0;
      }

      to {
          transform: translateX(0);
          opacity: 1;
      }
  }

  .animate-fade-in {
      animation: fadeIn 1s ease-out forwards;
  }

  .animate-pulse {
      animation: pulse 2s infinite;
  }

  .animate-slide-left {
      animation: slideInLeft 0.8s ease-out forwards;
  }

  .animate-slide-right {
      animation: slideInRight 0.8s ease-out forwards;
  }

  .delay-100 {
      animation-delay: 0.1s;
  }

  .delay-200 {
      animation-delay: 0.2s;
  }

  .delay-300 {
      animation-delay: 0.3s;
  }

  .delay-400 {
      animation-delay: 0.4s;
  }

  .delay-500 {
      animation-delay: 0.5s;
  }

  .gradient-bg {
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  }

  .car-slide {
      transition: transform 0.5s ease, opacity 0.5s ease;
  }

  .car-slide.active {
      opacity: 1;
      transform: scale(1);
      z-index: 10;
  }

  .car-slide.inactive {
      opacity: 0;
      transform: scale(0.8);
      z-index: 1;
  }

  .testimonial-card {
      transition: all 0.3s ease;
  }

  .testimonial-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .road-line {
      position: absolute;
      height: 2px;
      width: 100%;
      background: repeating-linear-gradient(to right, white, white 20px, transparent 20px, transparent 40px);
      animation: roadMove 1s linear infinite;
  }

  @keyframes roadMove {
      0% {
          transform: translateX(0);
      }

      100% {
          transform: translateX(-40px);
      }
  }

  .floating {
      animation: floating 3s ease-in-out infinite;
  }

  @keyframes floating {
      0% {
          transform: translateY(0px);
      }

      50% {
          transform: translateY(-15px);
      }

      100% {
          transform: translateY(0px);
      }
  }


 