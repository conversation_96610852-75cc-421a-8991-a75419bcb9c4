const rules = [
  { pattern: /hi|hello|hey/i, answer: "Hi there! How can I help you today?" },
  {
    pattern: /price|cost/i,
    answer: "Our pricing is flexible—tell me what you need built and I’ll estimate.",
  },
  {
    pattern: /services?|what do/i,
    answer: "We create responsive websites and mobile apps end-to-end.",
  },
  {
    pattern: /thanks|thank you/i,
    answer: "You’re welcome! Anything else I can do?",
  },

  // 🚗 Driving School Related
  {
    pattern: /driving school|learn driving/i,
    answer: "Yes! Metiabruz MTS offers beginner and premium driving lessons.",
  },
  {
    pattern: /license|licence|driving test/i,
    answer: "We help with learner’s license preparation and mock driving tests.",
  },
  {
    pattern: /timing|schedule|class time/i,
    answer: "Our classes are from 6 AM to 9 AM. Each session lasts around 45 minutes.",
  },
  {
    pattern: /fees|charges|course cost|course|package/i,
    answer: "Standard Course: ₹6500, Premium with Pickup & Drop: ₹7500 for 25 days.",
  },
  {
    pattern: /book.*class|enroll|join/i,
    answer: "To enroll, just send us your name, contact number, and preferred timing.",
  },
  {
    pattern: /vehicle|car|two-wheeler/i,
    answer: "We currently offer training for 4-wheeler cars only.",
  },
  {
    pattern: /duration|how long/i,
    answer: "Our course lasts for 25 days, with 4 km covered per session.",
  },
  {
    pattern: /pickup|home pickup|drop/i,
    answer: "Yes, our premium course includes free home pickup and drop!",
  },
  {
    pattern: /address|location/i,
    answer:
      "We’re located at:\n1. V-107 Dr A K Road, Kolkata - 700018\n2. X-34/A Akra Road, Kolkata - 700018",
  },
  {
    pattern: /contact|phone|whatsapp/i,
    answer:
      "📞 8240637341 (WhatsApp)\n📞 9331054551\n☎️ 033 24698595 (Office Landline)",
  },
  {
    pattern: /experience|since when/i,
    answer: "We have 26 years of experience in training (since 1999).",
  },
  {
    pattern: /facebook|social media/i,
    answer:
      "Follow us on Facebook: https://www.facebook.com/share/1AX6GDizGy/",
  },
  {
    pattern: /student.*done|how many student/i,
    answer: "Around 100 students complete their course with us every 3-4 months.",
  },
  {
    pattern: /course type|standard|premium/i,
    answer:
      "We offer 2 courses:\n1️⃣ Standard Course – ₹6500 (no pickup)\n2️⃣ Premium Course – ₹7500 (with pickup & drop)",
  },
  {
    pattern: /mts|metiabruz/i,
    answer: "Metiabruz MTS is your trusted driving school with a 98% pass rate!",
  },
  {
    pattern: /pass rate|success rate/i,
    answer: "We have a 98% pass rate—your success is our priority!",
  },
  {
    pattern: /how many days|course length|total duration/i,
    answer: "Our standard driving course is for a duration of 25 days."
  },
  {
    pattern: /daily practice|km per day|how much drive/i,
    answer: "Each class includes about 45 minutes of practice, covering 4 km of driving per day."
  },
  {
    pattern: /class duration|session time|how long is a class/i,
    answer: "Each driving session lasts for approximately 45 minutes."
  },
  {
    pattern: /what will I learn|course content|syllabus/i,
    answer: "Our course covers all essentials from basic car controls to on-road practice, traffic rules, and preparation for your driving test."
  },
  {
    pattern: /why two prices|price difference|what is the difference/i,
    answer: "The price difference is for the pickup and drop service. The standard course is ₹6,500, and the premium course with home pickup and drop is ₹7,500."
  },
  {
    pattern: /cheapest|minimum cost|basic price/i,
    answer: "Our most affordable option is the Standard Course at ₹6,500, which includes the full 25-day training."
  },
  {
    pattern: /installments|emi|payment plan/i,
    answer: "Please call us at 8240637341 to discuss available payment options."
  },
  {
    pattern: /evening batch|afternoon class|other timings/i,
    answer: "Currently, our main batch runs in the morning from 6:00 AM to 9:00 AM. Please call us to inquire about any special timing requests."
  },
  {
    pattern: /morning batch|when.*start in morning/i,
    answer: "Our morning classes are available from 6:00 AM to 9:00 AM. You can book a 45-minute slot within this window."
  },
  {
    pattern: /flexible timing|custom schedule/i,
    answer: "We try to be flexible! Please contact us with your preferred timings, and we'll see if we can accommodate you."
  },
  {
    pattern: /transport facility|provide transport/i,
    answer: "Yes, we offer a premium course for ₹7,500 which includes a convenient pickup and drop service from your location."
  },
  {
    pattern: /branches|which branch|office location/i,
    answer: "We have two locations for your convenience:\n1. V-107 Dr A K Road\n2. X-34/A Akra Road. You can visit whichever is closer to you."
  },
  {
    pattern: /which car|car model|training vehicle/i,
    answer: "We use standard, well-maintained hatchbacks for training, which are easy for beginners to learn on."
  },
  {
    pattern: /how old is school|experienced trainers|reliable/i,
    answer: "We are a highly reliable school with 26 years of experience since 1999. Our trainers are seasoned professionals."
  },
  {
    pattern: /success of students|guarantee pass/i,
    answer: "We have a very high success rate of 98%. While we can't guarantee a pass as it depends on the RTO test, we prepare you thoroughly to succeed."
  }
];

const messages = document.getElementById("messages");
const chatForm = document.getElementById("chatForm");
const userInput = document.getElementById("userInput");

function addMsg(text, from) {
  const bubble = document.createElement("div");
  bubble.className = `p-2 rounded-lg max-w-[75%] ${
    from === "user" ? "bg-blue-100 self-end" : "bg-gray-200"
  }`;
  bubble.innerText = text;
  messages.appendChild(bubble);
  messages.scrollTop = messages.scrollHeight;
}

function replyTo(text) {
  const rule = rules.find((r) => r.pattern.test(text));
  return rule ? rule.answer : "Sorry, I’m still learning. Could you rephrase?";
}

chatForm.addEventListener("submit", (e) => {
  e.preventDefault();
  const text = userInput.value.trim();
  if (!text) return;
  addMsg(text, "user");
  userInput.value = "";

  // small delay for realism
  setTimeout(() => addMsg(replyTo(text), "bot"), 400);
});